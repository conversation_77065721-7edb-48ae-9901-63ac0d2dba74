<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Submission App</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Code Submission Portal</h1>
        </header>

        <main>
            <form id="submissionForm">
                <!-- Language Selection -->
                <div class="form-group">
                    <label for="language">Programming Language:</label>
                    <select id="language" name="language" required>
                        <option value="">Select a language</option>
                        <option value="javascript">JavaScript</option>
                        <option value="python">Python</option>
                        <option value="java">Java</option>
                        <option value="cpp">C++</option>
                        <option value="c">C</option>
                        <option value="csharp">C#</option>
                        <option value="go">Go</option>
                        <option value="rust">Rust</option>
                        <option value="php">PHP</option>
                        <option value="ruby">Ruby</option>
                    </select>
                </div>

                <!-- Code Input -->
                <div class="form-group">
                    <label for="code">Code:</label>
                    <textarea id="code" name="code" placeholder="Enter your code here..." required></textarea>
                </div>

                <!-- Test Cases -->
                <div class="test-cases-section">
                    <h3>Test Cases</h3>
                    <div id="testCasesContainer">
                        <div class="test-case">
                            <div class="form-group">
                                <label>Test Case 1 - Input:</label>
                                <textarea class="test-input" placeholder="Enter test input..."></textarea>
                            </div>
                            <div class="form-group">
                                <label>Test Case 1 - Expected Output:</label>
                                <textarea class="test-output" placeholder="Enter expected output..."></textarea>
                            </div>
                        </div>
                    </div>
                    <button type="button" id="addTestCase" class="btn-secondary">Add Test Case</button>
                </div>

                <!-- Submit Button -->
                <div class="form-actions">
                    <button type="submit" id="submitBtn" class="btn-primary">Submit Code</button>
                </div>
            </form>

            <!-- Status Message -->
            <div id="statusMessage" class="status-message hidden"></div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
