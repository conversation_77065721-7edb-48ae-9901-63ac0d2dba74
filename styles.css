* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    color: #2c3e50;
    font-size: 2.5rem;
    margin-bottom: 10px;
}

main {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #2c3e50;
}

select, textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

select:focus, textarea:focus {
    outline: none;
    border-color: #3498db;
}

select {
    height: 45px;
    background-color: white;
}

textarea {
    min-height: 120px;
    resize: vertical;
    font-family: 'Courier New', monospace;
}

#code {
    min-height: 200px;
    font-size: 13px;
}

.test-cases-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #eee;
}

.test-cases-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.test-case {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 15px;
    border-left: 4px solid #3498db;
}

.test-case .form-group {
    margin-bottom: 15px;
}

.test-case:last-child {
    margin-bottom: 20px;
}

.test-input, .test-output {
    min-height: 80px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

.btn-primary, .btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
}

.btn-primary:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
    margin-bottom: 20px;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.form-actions {
    text-align: center;
    margin-top: 30px;
}

.status-message {
    margin-top: 20px;
    padding: 15px;
    border-radius: 5px;
    text-align: center;
    font-weight: 600;
}

.status-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-message.hidden {
    display: none;
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
