const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

let mainWindow;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // Load the HTML file
  mainWindow.loadFile('index.html');

  // Open DevTools in development
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }
}

// This method will be called when Electron has finished initialization
app.whenReady().then(createWindow);

// Quit when all windows are closed
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Handle IPC communication for saving data
ipcMain.handle('save-submission', async (event, data) => {
  try {
    // Create submissions directory if it doesn't exist
    const submissionsDir = path.join(__dirname, 'submissions');
    if (!fs.existsSync(submissionsDir)) {
      fs.mkdirSync(submissionsDir);
    }

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `submission_${timestamp}.json`;
    const filepath = path.join(submissionsDir, filename);

    // Prepare data to save
    const submissionData = {
      timestamp: new Date().toISOString(),
      language: data.language,
      code: data.code,
      testCases: data.testCases
    };

    // Write data to file
    fs.writeFileSync(filepath, JSON.stringify(submissionData, null, 2));

    return {
      success: true,
      message: `Data saved successfully to ${filename}`,
      filepath: filepath
    };
  } catch (error) {
    console.error('Error saving submission:', error);
    return {
      success: false,
      message: `Error saving data: ${error.message}`
    };
  }
});
