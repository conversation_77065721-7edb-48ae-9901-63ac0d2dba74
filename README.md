# Code Submission Electron App

A desktop application built with Electron for submitting code with test cases. The app provides a user-friendly interface for entering code, selecting programming languages, and defining test cases.

## Features

1. **Code Input**: Large text area for entering your code
2. **Language Selection**: Dropdown menu with popular programming languages
3. **Test Cases**: Dynamic test case input with ability to add/remove test cases
4. **File Saving**: Automatically saves submissions to JSON files with timestamps
5. **User Feedback**: Real-time status messages for successful saves or errors

## Installation

1. Make sure you have Node.js installed on your system
2. Clone or download this project
3. Open terminal in the project directory
4. Install dependencies:
   ```bash
   npm install
   ```

## Running the Application

### Method 1: Using npm script
```bash
npm start
```

### Method 2: Using npx
```bash
npx electron .
```

### Method 3: Direct execution (Windows)
```bash
node_modules/electron/dist/electron.exe .
```

## How to Use

1. **Select Language**: Choose your programming language from the dropdown
2. **Enter Code**: Type or paste your code in the large text area
3. **Add Test Cases**: 
   - Fill in the input and expected output for Test Case 1
   - Click "Add Test Case" to add more test cases
   - Use "Remove Test Case" button to delete unwanted test cases
4. **Submit**: Click the "Submit Code" button to save your submission
5. **Confirmation**: You'll see a success message when the data is saved

## File Storage

- Submissions are saved in the `submissions/` directory
- Each submission is saved as a JSON file with timestamp
- File format: `submission_YYYY-MM-DDTHH-mm-ss-sssZ.json`

## File Structure

```
├── main.js          # Electron main process
├── preload.js       # Preload script for secure IPC
├── index.html       # Main UI
├── styles.css       # Styling
├── script.js        # Frontend JavaScript
├── package.json     # Project configuration
└── submissions/     # Directory for saved submissions (created automatically)
```

## Submission Data Format

Each submission is saved as a JSON file with the following structure:

```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "language": "javascript",
  "code": "function hello() { return 'Hello World'; }",
  "testCases": [
    {
      "id": 1,
      "input": "hello()",
      "expectedOutput": "Hello World"
    }
  ]
}
```

## Development

To run in development mode with DevTools open:
```bash
npm run dev
```

## Troubleshooting

1. **App won't start**: Make sure all dependencies are installed with `npm install`
2. **Submission not saving**: Check if the app has write permissions in the project directory
3. **UI not loading**: Verify that all files (index.html, styles.css, script.js) are in the project root

## Technologies Used

- **Electron**: Desktop app framework
- **HTML/CSS/JavaScript**: Frontend interface
- **Node.js**: Backend file operations
- **IPC (Inter-Process Communication)**: Secure communication between frontend and backend
