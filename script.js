document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('submissionForm');
    const addTestCaseBtn = document.getElementById('addTestCase');
    const testCasesContainer = document.getElementById('testCasesContainer');
    const statusMessage = document.getElementById('statusMessage');
    const submitBtn = document.getElementById('submitBtn');

    let testCaseCount = 1;

    // Add new test case
    addTestCaseBtn.addEventListener('click', function() {
        testCaseCount++;
        const testCaseDiv = document.createElement('div');
        testCaseDiv.className = 'test-case';
        testCaseDiv.innerHTML = `
            <div class="form-group">
                <label>Test Case ${testCaseCount} - Input:</label>
                <textarea class="test-input" placeholder="Enter test input..."></textarea>
            </div>
            <div class="form-group">
                <label>Test Case ${testCaseCount} - Expected Output:</label>
                <textarea class="test-output" placeholder="Enter expected output..."></textarea>
            </div>
            <button type="button" class="btn-secondary remove-test-case">Remove Test Case</button>
        `;
        
        testCasesContainer.appendChild(testCaseDiv);

        // Add remove functionality
        const removeBtn = testCaseDiv.querySelector('.remove-test-case');
        removeBtn.addEventListener('click', function() {
            testCaseDiv.remove();
            updateTestCaseNumbers();
        });
    });

    // Update test case numbers after removal
    function updateTestCaseNumbers() {
        const testCases = testCasesContainer.querySelectorAll('.test-case');
        testCaseCount = testCases.length;
        
        testCases.forEach((testCase, index) => {
            const labels = testCase.querySelectorAll('label');
            labels[0].textContent = `Test Case ${index + 1} - Input:`;
            labels[1].textContent = `Test Case ${index + 1} - Expected Output:`;
        });
    }

    // Handle form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Show loading state
        showLoading();
        
        try {
            // Collect form data
            const language = document.getElementById('language').value;
            const code = document.getElementById('code').value;
            
            // Collect test cases
            const testCases = [];
            const testCaseElements = testCasesContainer.querySelectorAll('.test-case');
            
            testCaseElements.forEach((testCase, index) => {
                const input = testCase.querySelector('.test-input').value;
                const output = testCase.querySelector('.test-output').value;
                
                if (input.trim() || output.trim()) {
                    testCases.push({
                        id: index + 1,
                        input: input.trim(),
                        expectedOutput: output.trim()
                    });
                }
            });

            // Validate required fields
            if (!language) {
                throw new Error('Please select a programming language');
            }
            
            if (!code.trim()) {
                throw new Error('Please enter your code');
            }

            if (testCases.length === 0) {
                throw new Error('Please add at least one test case');
            }

            // Prepare data for submission
            const submissionData = {
                language: language,
                code: code.trim(),
                testCases: testCases
            };

            // Send data to Electron main process
            const result = await window.electronAPI.saveSubmission(submissionData);
            
            if (result.success) {
                showMessage(result.message, 'success');
                // Optionally reset form
                // form.reset();
                // resetTestCases();
            } else {
                throw new Error(result.message);
            }
            
        } catch (error) {
            showMessage(error.message, 'error');
        } finally {
            hideLoading();
        }
    });

    // Show loading state
    function showLoading() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="loading"></span>Saving...';
    }

    // Hide loading state
    function hideLoading() {
        submitBtn.disabled = false;
        submitBtn.innerHTML = 'Submit Code';
    }

    // Show status message
    function showMessage(message, type) {
        statusMessage.textContent = message;
        statusMessage.className = `status-message ${type}`;
        statusMessage.scrollIntoView({ behavior: 'smooth' });
        
        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                statusMessage.className = 'status-message hidden';
            }, 5000);
        }
    }

    // Reset test cases to initial state
    function resetTestCases() {
        testCasesContainer.innerHTML = `
            <div class="test-case">
                <div class="form-group">
                    <label>Test Case 1 - Input:</label>
                    <textarea class="test-input" placeholder="Enter test input..."></textarea>
                </div>
                <div class="form-group">
                    <label>Test Case 1 - Expected Output:</label>
                    <textarea class="test-output" placeholder="Enter expected output..."></textarea>
                </div>
            </div>
        `;
        testCaseCount = 1;
    }
});
